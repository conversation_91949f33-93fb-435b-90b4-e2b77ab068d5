<?php
session_start();
require_once 'conexao.php';
require_once 'verificar_permissao.php';
require_once 'security_functions.php';
require_once 'config.php';

// Verificar timeout da sessão
checkSessionTimeout();

// Regenerar ID da sessão periodicamente
regenerateSessionIfNeeded();

// Verificar se o usuário está logado
if (!isset($_SESSION['userName']) || $_SESSION['userName'] == null) {
    header("Location: Projeto.php?msg=" . urlencode("Faça login para acessar o sistema"));
    exit();
}

// Verificar permissão para acessar a página de horas
if (!isset($_SESSION['cargo_utilizador']) || !checkMenuAccess($_SESSION['cargo_utilizador'], 'HORAS')) {
    header("Location: Projeto.php?msg=" . urlencode("Você não tem permissão para acessar esta página"));
    exit();
}

// Verificar se o ID foi fornecido
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['mensagem'] = "ID inválido.";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: Projeto pag 4.php");
    exit();
}

$id = intval($_GET['id']);
$conn = connectToDatabase();

// Descobrir o nome correto da coluna ID
$result_columns = mysqli_query($conn, "SHOW COLUMNS FROM registro_horas");
$id_column_name = null;

if ($result_columns) {
    while ($column = mysqli_fetch_assoc($result_columns)) {
        if ($column['Key'] == 'PRI') {
            $id_column_name = $column['Field'];
            break;
        }
    }
}

// Se não encontrou pela chave primária, tenta alguns nomes comuns
if (!$id_column_name) {
    $possible_id_names = ['id', 'registro_id', 'id_registro', 'hora_id', 'id_hora', 'registro_horas_id'];
    $table_columns = [];
    
    $result_columns = mysqli_query($conn, "SHOW COLUMNS FROM registro_horas");
    while ($column = mysqli_fetch_assoc($result_columns)) {
        $table_columns[] = $column['Field'];
    }
    
    foreach ($possible_id_names as $possible_name) {
        if (in_array($possible_name, $table_columns)) {
            $id_column_name = $possible_name;
            break;
        }
    }
}

// Se ainda não encontrou, tenta usar o nome da coluna que está sendo usada na página de listagem
if (!$id_column_name) {
    $id_column_name = 'registro_id'; // Nome usado na consulta da página de listagem
}

// Verificar se o registro existe
$query_check = "SELECT * FROM registro_horas WHERE $id_column_name = ?";
$stmt_check = mysqli_prepare($conn, $query_check);
mysqli_stmt_bind_param($stmt_check, "i", $id);
mysqli_stmt_execute($stmt_check);
$result_check = mysqli_stmt_get_result($stmt_check);

if (mysqli_num_rows($result_check) == 0) {
    $_SESSION['mensagem'] = "Registro não encontrado.";
    $_SESSION['tipo_mensagem'] = "danger";
    header("Location: Projeto pag 4.php");
    exit();
}

// Excluir o registro
$query_delete = "DELETE FROM registro_horas WHERE $id_column_name = ?";
$stmt_delete = mysqli_prepare($conn, $query_delete);
mysqli_stmt_bind_param($stmt_delete, "i", $id);

if (mysqli_stmt_execute($stmt_delete)) {
    $_SESSION['mensagem'] = "Registro excluído com sucesso!";
    $_SESSION['tipo_mensagem'] = "success";
} else {
    $_SESSION['mensagem'] = "Erro ao excluir registro: " . mysqli_error($conn);
    $_SESSION['tipo_mensagem'] = "danger";
}

mysqli_stmt_close($stmt_check);
mysqli_stmt_close($stmt_delete);
mysqli_close($conn);

// Redirecionar de volta para a página de horas
header("Location: Projeto pag 4.php");
exit();
?>




